from enum import IntFlag

import comtypes.gen._944DE083_8FB8_45CF_BCB7_C477ACB2F897_0_1_0 as __wrapper_module__
from comtypes.gen._944DE083_8FB8_45CF_BCB7_C477ACB2F897_0_1_0 import (
    UIA_BulletStyleAttributeId, UIA_FontSizeAttributeId,
    ZoomUnit_LargeIncrement, UIA_RangeValueMaximumPropertyId,
    TextUnit_Word, IUIAutomationTextEditPattern,
    UIA_IsSpreadsheetItemPatternAvailablePropertyId,
    SynchronizedInputType_KeyUp, UIA_IndentationTrailingAttributeId,
    UIA_SizePropertyId, UIA_IsTextPattern2AvailablePropertyId,
    UIA_AnnotationTypesAttributeId,
    UIA_IsSelectionPatternAvailablePropertyId,
    UIA_SeparatorControlTypeId, IUIAutomationItemContainerPattern,
    UIA_StylesFillColorPropertyId,
    UIA_LegacyIAccessibleRolePropertyId, UIA_AriaRolePropertyId,
    AnnotationType_Endnote, UIA_AutomationFocusChangedEventId,
    UIA_StylesStyleNamePropertyId,
    UIA_IsDockPatternAvailablePropertyId, _lcid,
    UIA_IsLegacyIAccessiblePatternAvailablePropertyId, Library,
    UIA_LegacyIAccessibleNamePropertyId,
    AnnotationType_AdvancedProofingIssue,
    CoalesceEventsOptions_Enabled, RowOrColumnMajor_RowMajor,
    ToggleState_Off, IUIAutomation, UIA_DocumentControlTypeId,
    UIA_SayAsInterpretAsMetadataId,
    StructureChangeType_ChildrenBulkRemoved,
    UIA_IsSelectionPattern2AvailablePropertyId,
    AnnotationType_Highlighted,
    UIA_LegacyIAccessibleKeyboardShortcutPropertyId,
    TextUnit_Character, UIA_DropTargetDropTargetEffectsPropertyId,
    IUIAutomationSpreadsheetItemPattern, UIA_ValueValuePropertyId,
    NavigateDirection_Parent, UIA_Transform2ZoomMinimumPropertyId,
    UIA_InvokePatternId, UIA_InputReachedOtherElementEventId,
    UIA_AfterParagraphSpacingAttributeId, UIA_FillColorPropertyId,
    UIA_IndentationFirstLineAttributeId, UIA_StylesStyleIdPropertyId,
    AnnotationType_GrammarError, IUIAutomation3,
    UIA_Transform2ZoomMaximumPropertyId, UIA_OutlineStylesAttributeId,
    UIA_GridItemContainingGridPropertyId,
    UIA_IsMultipleViewPatternAvailablePropertyId,
    ProviderOptions_ClientSideProvider, UIA_ItemContainerPatternId,
    IUIAutomationStructureChangedEventHandler,
    RowOrColumnMajor_ColumnMajor,
    UIA_SpreadsheetItemFormulaPropertyId, UIA_IsHiddenAttributeId,
    StructureChangeType_ChildAdded, UIA_TablePatternId,
    IUIAutomationOrCondition, SupportedTextSelection_Single,
    NavigateDirection_FirstChild, UIA_RuntimeIdPropertyId,
    ToggleState_On, UIA_RangeValueIsReadOnlyPropertyId,
    UIA_MenuOpenedEventId, UIA_DataItemControlTypeId,
    UIA_SystemAlertEventId, IUIAutomationTransformPattern2,
    UIA_GridItemColumnSpanPropertyId,
    UIA_MultipleViewSupportedViewsPropertyId,
    AnnotationType_InsertionChange, ProviderOptions_UseComThreading,
    StyleId_Heading8, UIA_FlowsToPropertyId,
    UIA_Transform2CanZoomPropertyId, UIA_StructureChangedEventId,
    IUIAutomationTextRange2,
    UIA_IsRangeValuePatternAvailablePropertyId, UIA_ListControlTypeId,
    _midlSAFEARRAY, WindowInteractionState_BlockedByModalWindow,
    UIA_ScrollHorizontallyScrollablePropertyId,
    IUIAutomationGridPattern, UIA_AutomationPropertyChangedEventId,
    UIA_MainLandmarkTypeId, TextEditChangeType_AutoComplete,
    AnnotationType_Header, UIA_Transform2ZoomLevelPropertyId,
    UIA_LocalizedControlTypePropertyId,
    UIA_IsTextChildPatternAvailablePropertyId,
    UIA_SizeOfSetPropertyId, StructureChangeType_ChildRemoved,
    UIA_RangeValueValuePropertyId, UIA_GridItemColumnPropertyId,
    UIA_ControllerForPropertyId, UIA_LinkAttributeId,
    NotificationProcessing_MostRecent, TreeScope_Descendants,
    UIA_DropTargetDropTargetEffectPropertyId, dispid,
    AnnotationType_EditingLockedChange, IUIAutomationTextPattern,
    IUIAutomationEventHandler, UIA_Window_WindowClosedEventId,
    UIA_SummaryChangeId, HeadingLevel6,
    UIA_TableItemRowHeaderItemsPropertyId,
    UIA_InputReachedTargetEventId,
    StructureChangeType_ChildrenInvalidated,
    UIA_SelectionItem_ElementSelectedEventId, Assertive,
    UIA_CaretBidiModeAttributeId, UIA_IsItalicAttributeId,
    StyleId_Heading1, UIA_AppBarControlTypeId,
    UIA_IsTransformPattern2AvailablePropertyId, Polite,
    UIA_CustomLandmarkTypeId, CUIAutomation,
    NotificationProcessing_ImportantAll, UIA_SelectionItemPatternId,
    UIA_ControlTypePropertyId, UIA_AnnotationTargetPropertyId,
    IUIAutomationStylesPattern,
    UIA_WindowWindowInteractionStatePropertyId,
    UIA_UnderlineStyleAttributeId,
    UIA_ActiveTextPositionChangedEventId,
    IUIAutomationDropTargetPattern, UIA_WindowControlTypeId,
    UIA_SelectionPatternId, UIA_LegacyIAccessibleChildIdPropertyId,
    UIA_StylesExtendedPropertiesPropertyId, NotificationKind_Other,
    UIA_LocalizedLandmarkTypePropertyId,
    IUIAutomationChangesEventHandler,
    WindowInteractionState_NotResponding, OrientationType_None,
    UIA_IsExpandCollapsePatternAvailablePropertyId,
    UIA_SynchronizedInputPatternId, IUIAutomationElement6,
    DockPosition_None, ToggleState_Indeterminate,
    UIA_DropTargetPatternId, UIA_MenuModeStartEventId,
    UIA_FrameworkIdPropertyId, UIA_TreeItemControlTypeId,
    TreeScope_Element, UIA_TextEdit_ConversionTargetChangedEventId,
    CoClass, UIA_CultureAttributeId, UIA_SayAsInterpretAsAttributeId,
    NavigateDirection_NextSibling, UIA_LayoutInvalidatedEventId,
    IUIAutomationTableItemPattern, UIA_ItemTypePropertyId,
    IUIAutomation5, UIA_ForegroundColorAttributeId,
    UIA_ImageControlTypeId, UIA_TableItemPatternId,
    AnnotationType_DeletionChange, UIA_TableControlTypeId,
    UiaChangeInfo, UIA_Text_TextChangedEventId, UIA_NamePropertyId,
    UIA_CaretPositionAttributeId, IUIAutomationElement2,
    IUIAutomationInvokePattern, UIA_CheckBoxControlTypeId,
    UIA_PaneControlTypeId, AnnotationType_Footnote,
    UIA_BackgroundColorAttributeId,
    UIA_LegacyIAccessibleSelectionPropertyId, ZoomUnit_SmallIncrement,
    IUIAutomationElement9, IUIAutomationTogglePattern, HeadingLevel4,
    UIA_LegacyIAccessibleStatePropertyId, UIA_FontNameAttributeId,
    IUIAutomationTransformPattern, UIA_SpinnerControlTypeId,
    DockPosition_Right, UIA_SpreadsheetItemAnnotationTypesPropertyId,
    TextPatternRangeEndpoint_End,
    NotificationProcessing_CurrentThenMostRecent,
    UIA_TransformCanRotatePropertyId,
    IUIAutomationSynchronizedInputPattern, UIA_ToolTipOpenedEventId,
    UIA_AccessKeyPropertyId, UIA_AnnotationTypesPropertyId,
    UIA_ScrollVerticalScrollPercentPropertyId, StyleId_Heading9,
    UIA_IsTablePatternAvailablePropertyId, StyleId_Subtitle,
    TreeTraversalOptions_PostOrder, UIA_ValuePatternId,
    IUIAutomationScrollItemPattern, NavigateDirection_PreviousSibling,
    IUIAutomationElement5, StructureChangeType_ChildrenBulkAdded,
    UIA_LegacyIAccessibleDefaultActionPropertyId,
    IUIAutomationAnnotationPattern, AnnotationType_Author,
    UIA_Drag_DragCancelEventId, UIA_GridItemRowPropertyId,
    UIA_SearchLandmarkTypeId, UIA_IsPasswordPropertyId,
    UIA_StyleNameAttributeId, SynchronizedInputType_RightMouseDown,
    UIA_IsSynchronizedInputPatternAvailablePropertyId,
    IUIAutomationTablePattern, UIA_AnnotationObjectsAttributeId,
    StyleId_Heading4, ProviderOptions_OverrideProvider,
    IUIAutomationProxyFactoryEntry, TextUnit_Page,
    UIA_LabeledByPropertyId, UIA_FullDescriptionPropertyId,
    NotificationKind_ItemAdded, IUIAutomationDragPattern,
    WindowInteractionState_Closing,
    IUIAutomationFocusChangedEventHandler, UIA_StyleIdAttributeId,
    DockPosition_Bottom, UIA_IsDragPatternAvailablePropertyId,
    AnnotationType_Unknown, UIA_TextEdit_TextChangedEventId,
    UIA_LandmarkTypePropertyId, AnnotationType_TrackChanges,
    ScrollAmount_SmallDecrement, UIA_GridPatternId,
    UIA_SelectionItemIsSelectedPropertyId,
    IUIAutomationLegacyIAccessiblePattern,
    UIA_WindowCanMinimizePropertyId, TextEditChangeType_AutoCorrect,
    UIA_SelectionItem_ElementAddedToSelectionEventId,
    UIA_Drag_DragStartEventId, TreeScope_Parent,
    UIA_IsCustomNavigationPatternAvailablePropertyId,
    UIA_ScrollItemPatternId, UIA_IsGridItemPatternAvailablePropertyId,
    UIA_DockDockPositionPropertyId, TextEditChangeType_Composition,
    ProviderOptions_HasNativeIAccessible,
    UIA_IsContentElementPropertyId,
    UIA_RangeValueLargeChangePropertyId,
    UIA_IsWindowPatternAvailablePropertyId, TextUnit_Document,
    UIA_SelectionItem_ElementRemovedFromSelectionEventId,
    TextUnit_Format, UIA_LiveRegionChangedEventId, DockPosition_Left,
    UIA_TableItemColumnHeaderItemsPropertyId, IAccessible,
    UIA_LiveSettingPropertyId, UIA_GridRowCountPropertyId,
    ProviderOptions_UseClientCoordinates,
    UIA_IndentationLeadingAttributeId, HeadingLevel_None,
    UIA_IsScrollItemPatternAvailablePropertyId,
    IUIAutomationMultipleViewPattern, UIA_CapStyleAttributeId,
    IUIAutomationExpandCollapsePattern,
    NotificationProcessing_ImportantMostRecent, UIA_MenuClosedEventId,
    IUIAutomationTextRange3, UIA_HyperlinkControlTypeId,
    HeadingLevel1, UIA_IsAnnotationPatternAvailablePropertyId,
    RowOrColumnMajor_Indeterminate, UIA_CalendarControlTypeId,
    UIA_IsSubscriptAttributeId,
    UIA_SpreadsheetItemAnnotationObjectsPropertyId,
    IUIAutomationTextPattern2, UIA_HorizontalTextAlignmentAttributeId,
    DockPosition_Fill, IUIAutomationProxyFactoryMapping,
    UIA_IsTextEditPatternAvailablePropertyId,
    UIA_OutlineColorPropertyId, StyleId_Emphasis,
    IUIAutomationElement7, UIA_StrikethroughColorAttributeId,
    UIA_SelectionCanSelectMultiplePropertyId,
    UIA_ComboBoxControlTypeId, IUIAutomationValuePattern,
    IUIAutomationWindowPattern, tagPOINT, AnnotationType_Footer,
    UIA_NativeWindowHandlePropertyId, UIA_DropTarget_DragLeaveEventId,
    BSTR, UIA_ProcessIdPropertyId, ProviderOptions_ServerSideProvider,
    UIA_TransformPattern2Id, UIA_ExpandCollapsePatternId,
    IUIAutomation2, UIA_TextEditPatternId,
    UIA_DragDropEffectsPropertyId, UIA_ValueIsReadOnlyPropertyId,
    UIA_IsTogglePatternAvailablePropertyId,
    ConnectionRecoveryBehaviorOptions_Enabled,
    UIA_IsDropTargetPatternAvailablePropertyId,
    AnnotationType_SpellingError, HRESULT, UIA_IsPeripheralPropertyId,
    AnnotationType_UnsyncedChange, CUIAutomation8,
    UIA_IsObjectModelPatternAvailablePropertyId,
    UIA_LegacyIAccessiblePatternId, UIA_AnnotationDateTimePropertyId,
    UIA_SemanticZoomControlTypeId,
    UIA_Selection2CurrentSelectedItemPropertyId,
    IUIAutomationCondition, IUIAutomationSelectionPattern2,
    UIA_ScrollPatternId, UIA_RangeValuePatternId,
    UIA_MenuModeEndEventId, HeadingLevel9,
    UIA_WindowIsModalPropertyId, UIA_MenuItemControlTypeId,
    UIA_MarginBottomAttributeId,
    UIA_WindowWindowVisualStatePropertyId, UIA_TreeControlTypeId,
    UIA_HostedFragmentRootsInvalidatedEventId, StyleId_Heading6,
    IUIAutomationTreeWalker, typelib_path, UIA_ItemStatusPropertyId,
    IUIAutomationPropertyCondition, NavigateDirection_LastChild,
    IUIAutomationElementArray, UIA_IsDialogPropertyId,
    UIA_FontWeightAttributeId, UIA_ScrollHorizontalViewSizePropertyId,
    UIA_ProviderDescriptionPropertyId, IUIAutomationElement,
    NotificationKind_ActionAborted,
    UIA_TableRowOrColumnMajorPropertyId,
    IUIAutomationCustomNavigationPattern, ScrollAmount_NoAmount,
    PropertyConditionFlags_MatchSubstring,
    ConnectionRecoveryBehaviorOptions_Disabled, IUIAutomation6,
    UIA_ThumbControlTypeId, UIA_ButtonControlTypeId,
    UIA_Drag_DragCompleteEventId, IUIAutomationElement4,
    AnnotationType_Comment, ExpandCollapseState_PartiallyExpanded,
    UIA_IsSelectionItemPatternAvailablePropertyId,
    UIA_LegacyIAccessibleValuePropertyId, ZoomUnit_SmallDecrement,
    SupportedTextSelection_None,
    UIA_IsVirtualizedItemPatternAvailablePropertyId,
    TreeScope_Ancestors, UIA_Selection_InvalidatedEventId,
    UIA_IsDataValidForFormPropertyId, HeadingLevel2,
    UIA_TableColumnHeadersPropertyId, ScrollAmount_LargeDecrement,
    UIA_Window_WindowOpenedEventId, UIA_EditControlTypeId,
    UIA_Invoke_InvokedEventId, TreeTraversalOptions_LastToFirstOrder,
    IUIAutomationElement3, CoalesceEventsOptions_Disabled,
    UIA_TextFlowDirectionsAttributeId, UIA_OverlineColorAttributeId,
    UIA_ExpandCollapseExpandCollapseStatePropertyId,
    UIA_IsEnabledPropertyId, ExpandCollapseState_Expanded,
    UIA_IsSuperscriptAttributeId, AnnotationType_DataValidationError,
    UIA_FormLandmarkTypeId, IUIAutomationSelectionPattern,
    AutomationElementMode_None, AnnotationType_MoveChange,
    UIA_CustomNavigationPatternId, AnnotationType_ConflictingChange,
    UIA_MultipleViewPatternId, GUID, SynchronizedInputType_KeyDown,
    UIA_StatusBarControlTypeId, UIA_OrientationPropertyId,
    UIA_TextPatternId, UIA_WindowCanMaximizePropertyId,
    AnnotationType_FormatChange, UIA_ToolBarControlTypeId,
    TextEditChangeType_None, UIA_OverlineStyleAttributeId,
    IUIAutomationSelectionItemPattern, IUIAutomationElement8,
    IUIAutomationNotificationEventHandler,
    UIA_AnnotationAnnotationTypeIdPropertyId,
    IUIAutomationVirtualizedItemPattern, IUIAutomationScrollPattern,
    UIA_ToolTipControlTypeId, UIA_FillTypePropertyId,
    UIA_DragDropEffectPropertyId,
    UIA_ScrollHorizontalScrollPercentPropertyId,
    UIA_TextChildPatternId, UIA_DragPatternId,
    SynchronizedInputType_LeftMouseDown,
    UIA_TableRowHeadersPropertyId, UIA_IsKeyboardFocusablePropertyId,
    ExpandCollapseState_Collapsed, UIA_BoundingRectanglePropertyId,
    UIA_WindowPatternId, UIA_CustomControlTypeId,
    ProviderOptions_NonClientAreaProvider,
    UIA_IsControlElementPropertyId,
    IUIAutomationPropertyChangedEventHandler,
    UIA_RangeValueSmallChangePropertyId,
    NotificationKind_ActionCompleted,
    UIA_StrikethroughStyleAttributeId, UIA_TextPattern2Id,
    UIA_TransformPatternId, ExtendedProperty,
    UIA_OutlineThicknessPropertyId, TextPatternRangeEndpoint_Start,
    UIA_IsTableItemPatternAvailablePropertyId,
    UIA_IsReadOnlyAttributeId, UIA_SelectionPattern2Id,
    UIA_InputDiscardedEventId, UIA_StylesPatternId,
    UIA_ChangesEventId, UIA_IsOffscreenPropertyId,
    WindowInteractionState_ReadyForUserInteraction,
    OrientationType_Horizontal, HeadingLevel8,
    UIA_IsTransformPatternAvailablePropertyId, Off,
    AutomationElementMode_Full, AnnotationType_FormulaError,
    ExpandCollapseState_LeafNode, UIA_TabItemControlTypeId,
    UIA_NotificationEventId, StyleId_Normal,
    UIA_DropTarget_DragEnterEventId,
    UIA_StylesFillPatternColorPropertyId,
    UIA_AnnotationAuthorPropertyId, UIA_MarginTrailingAttributeId,
    UIA_VisualEffectsPropertyId, UIA_ClassNamePropertyId,
    IUIAutomationEventHandlerGroup,
    UIA_LegacyIAccessibleDescriptionPropertyId, COMMETHOD,
    UIA_MenuBarControlTypeId, IUnknown,
    UIA_Selection2FirstSelectedItemPropertyId, StyleId_Heading5,
    UIA_IsGridPatternAvailablePropertyId,
    IUIAutomationTextEditTextChangedEventHandler, TextUnit_Line,
    IDispatch, OrientationType_Vertical,
    UIA_UnderlineColorAttributeId, UIA_HeaderItemControlTypeId,
    StyleId_BulletedList, UIA_SelectionIsSelectionRequiredPropertyId,
    UIA_GridItemRowSpanPropertyId, UIA_TextControlTypeId,
    IUIAutomationObjectModelPattern, WindowInteractionState_Running,
    IUIAutomationAndCondition, UIA_LevelPropertyId,
    UIA_IsScrollPatternAvailablePropertyId, IUIAutomation4,
    StructureChangeType_ChildrenReordered,
    UIA_TransformCanMovePropertyId, UIA_DragIsGrabbedPropertyId,
    StyleId_Custom, AnnotationType_ExternalChange,
    PropertyConditionFlags_IgnoreCase, UIA_SliderControlTypeId,
    UIA_DropTarget_DroppedEventId, UIA_GridItemPatternId,
    IUIAutomationActiveTextPositionChangedEventHandler,
    UIA_IsStylesPatternAvailablePropertyId,
    UIA_HeadingLevelPropertyId, UIA_FlowsFromPropertyId,
    NotificationProcessing_All, ZoomUnit_LargeDecrement,
    UIA_MarginLeadingAttributeId,
    UIA_IsSpreadsheetPatternAvailablePropertyId,
    UIA_RadioButtonControlTypeId, UIA_SplitButtonControlTypeId,
    UIA_DockPatternId, StyleId_Quote,
    ProviderOptions_RefuseNonClientSupport, UIA_IsActiveAttributeId,
    TreeScope_Subtree, UIA_MenuControlTypeId,
    UIA_RangeValueMinimumPropertyId, UIA_PositionInSetPropertyId,
    UIA_IsItemContainerPatternAvailablePropertyId,
    UIA_SpreadsheetItemPatternId, IUIAutomationCacheRequest,
    IUIAutomationTextRangeArray, NotificationKind_ItemRemoved,
    UIA_OptimizeForVisualContentPropertyId,
    IUIAutomationTextChildPattern, HeadingLevel3,
    UIA_ProgressBarControlTypeId, UIA_MarginTopAttributeId,
    UIA_LegacyIAccessibleHelpPropertyId, PropertyConditionFlags_None,
    TreeScope_None, UIA_LineSpacingAttributeId,
    IUIAutomationNotCondition, ScrollAmount_SmallIncrement,
    UIA_Selection2ItemCountPropertyId, UIA_DescribedByPropertyId,
    UIA_TitleBarControlTypeId, StyleId_Title,
    UIA_SelectionItemSelectionContainerPropertyId,
    UIA_CenterPointPropertyId, IUIAutomationSpreadsheetPattern,
    UIA_CulturePropertyId, UIA_IsRequiredForFormPropertyId,
    ZoomUnit_NoAmount, UIA_SpreadsheetPatternId, _check_version,
    VARIANT, HeadingLevel5, UIA_AnimationStyleAttributeId,
    UIA_WindowIsTopmostPropertyId,
    ProviderOptions_ProviderOwnsSetFocus, TreeScope_Children,
    UIA_ListItemControlTypeId, UIA_StylesFillPatternStylePropertyId,
    IRawElementProviderSimple,
    UIA_ScrollVerticallyScrollablePropertyId,
    WindowVisualState_Normal, UIA_DataGridControlTypeId,
    UIA_SelectionSelectionPropertyId, IUIAutomationRangeValuePattern,
    UIA_HeaderControlTypeId, WindowVisualState_Maximized,
    UIA_NavigationLandmarkTypeId, UIA_ScrollBarControlTypeId,
    UIA_IsInvokePatternAvailablePropertyId,
    IUIAutomationGridItemPattern, SupportedTextSelection_Multiple,
    UIA_HasKeyboardFocusPropertyId, WindowVisualState_Minimized,
    UIA_Text_TextSelectionChangedEventId, UIA_GroupControlTypeId,
    HeadingLevel7, UIA_AutomationIdPropertyId,
    UIA_AsyncContentLoadedEventId, IUIAutomationTextRange,
    UIA_TransformCanResizePropertyId,
    SynchronizedInputType_RightMouseUp, AnnotationType_Sensitive,
    UIA_MultipleViewCurrentViewPropertyId, AnnotationType_Mathematics,
    UIA_ClickablePointPropertyId,
    TextEditChangeType_CompositionFinalized, UIA_TabControlTypeId,
    AnnotationType_CircularReferenceError, IUIAutomationBoolCondition,
    UIA_AnnotationObjectsPropertyId, DockPosition_Top,
    UIA_AcceleratorKeyPropertyId, StyleId_NumberedList,
    UIA_ToolTipClosedEventId, StyleId_Heading3, UIA_TogglePatternId,
    IUIAutomationDockPattern,
    UIA_AnnotationAnnotationTypeNamePropertyId,
    UIA_ScrollVerticalViewSizePropertyId, StyleId_Heading2,
    UIA_AriaPropertiesPropertyId, UIA_RotationPropertyId,
    UIA_HelpTextPropertyId, UIA_BeforeParagraphSpacingAttributeId,
    UIA_ObjectModelPatternId, UIA_GridColumnCountPropertyId,
    ScrollAmount_LargeIncrement, TreeTraversalOptions_Default,
    UIA_SelectionActiveEndAttributeId, TextUnit_Paragraph,
    IUIAutomationProxyFactory, UIA_IsTextPatternAvailablePropertyId,
    UIA_StylesShapePropertyId, UIA_VirtualizedItemPatternId,
    UIA_AnnotationPatternId, UIA_DragGrabbedItemsPropertyId,
    SynchronizedInputType_LeftMouseUp, tagRECT, UIA_TabsAttributeId,
    WSTRING, UIA_ToggleToggleStatePropertyId, StyleId_Heading7,
    UIA_Selection2LastSelectedItemPropertyId,
    UIA_IsValuePatternAvailablePropertyId
)


class SynchronizedInputType(IntFlag):
    SynchronizedInputType_KeyUp = 1
    SynchronizedInputType_KeyDown = 2
    SynchronizedInputType_LeftMouseUp = 4
    SynchronizedInputType_LeftMouseDown = 8
    SynchronizedInputType_RightMouseUp = 16
    SynchronizedInputType_RightMouseDown = 32


class RowOrColumnMajor(IntFlag):
    RowOrColumnMajor_RowMajor = 0
    RowOrColumnMajor_ColumnMajor = 1
    RowOrColumnMajor_Indeterminate = 2


class ToggleState(IntFlag):
    ToggleState_Off = 0
    ToggleState_On = 1
    ToggleState_Indeterminate = 2


class WindowVisualState(IntFlag):
    WindowVisualState_Normal = 0
    WindowVisualState_Maximized = 1
    WindowVisualState_Minimized = 2


class WindowInteractionState(IntFlag):
    WindowInteractionState_Running = 0
    WindowInteractionState_Closing = 1
    WindowInteractionState_ReadyForUserInteraction = 2
    WindowInteractionState_BlockedByModalWindow = 3
    WindowInteractionState_NotResponding = 4


class ScrollAmount(IntFlag):
    ScrollAmount_LargeDecrement = 0
    ScrollAmount_SmallDecrement = 1
    ScrollAmount_NoAmount = 2
    ScrollAmount_LargeIncrement = 3
    ScrollAmount_SmallIncrement = 4


class TextPatternRangeEndpoint(IntFlag):
    TextPatternRangeEndpoint_Start = 0
    TextPatternRangeEndpoint_End = 1


class TextUnit(IntFlag):
    TextUnit_Character = 0
    TextUnit_Format = 1
    TextUnit_Word = 2
    TextUnit_Line = 3
    TextUnit_Paragraph = 4
    TextUnit_Page = 5
    TextUnit_Document = 6


class SupportedTextSelection(IntFlag):
    SupportedTextSelection_None = 0
    SupportedTextSelection_Single = 1
    SupportedTextSelection_Multiple = 2


class NavigateDirection(IntFlag):
    NavigateDirection_Parent = 0
    NavigateDirection_NextSibling = 1
    NavigateDirection_PreviousSibling = 2
    NavigateDirection_FirstChild = 3
    NavigateDirection_LastChild = 4


class StructureChangeType(IntFlag):
    StructureChangeType_ChildAdded = 0
    StructureChangeType_ChildRemoved = 1
    StructureChangeType_ChildrenInvalidated = 2
    StructureChangeType_ChildrenBulkAdded = 3
    StructureChangeType_ChildrenBulkRemoved = 4
    StructureChangeType_ChildrenReordered = 5


class ZoomUnit(IntFlag):
    ZoomUnit_NoAmount = 0
    ZoomUnit_LargeDecrement = 1
    ZoomUnit_SmallDecrement = 2
    ZoomUnit_LargeIncrement = 3
    ZoomUnit_SmallIncrement = 4


class TextEditChangeType(IntFlag):
    TextEditChangeType_None = 0
    TextEditChangeType_AutoCorrect = 1
    TextEditChangeType_Composition = 2
    TextEditChangeType_CompositionFinalized = 3
    TextEditChangeType_AutoComplete = 4


class TreeScope(IntFlag):
    TreeScope_None = 0
    TreeScope_Element = 1
    TreeScope_Children = 2
    TreeScope_Descendants = 4
    TreeScope_Parent = 8
    TreeScope_Ancestors = 16
    TreeScope_Subtree = 7


class OrientationType(IntFlag):
    OrientationType_None = 0
    OrientationType_Horizontal = 1
    OrientationType_Vertical = 2


class LiveSetting(IntFlag):
    Off = 0
    Polite = 1
    Assertive = 2


class TreeTraversalOptions(IntFlag):
    TreeTraversalOptions_Default = 0
    TreeTraversalOptions_PostOrder = 1
    TreeTraversalOptions_LastToFirstOrder = 2


class DockPosition(IntFlag):
    DockPosition_Top = 0
    DockPosition_Left = 1
    DockPosition_Bottom = 2
    DockPosition_Right = 3
    DockPosition_Fill = 4
    DockPosition_None = 5


class ExpandCollapseState(IntFlag):
    ExpandCollapseState_Collapsed = 0
    ExpandCollapseState_Expanded = 1
    ExpandCollapseState_PartiallyExpanded = 2
    ExpandCollapseState_LeafNode = 3


class ProviderOptions(IntFlag):
    ProviderOptions_ClientSideProvider = 1
    ProviderOptions_ServerSideProvider = 2
    ProviderOptions_NonClientAreaProvider = 4
    ProviderOptions_OverrideProvider = 8
    ProviderOptions_ProviderOwnsSetFocus = 16
    ProviderOptions_UseComThreading = 32
    ProviderOptions_RefuseNonClientSupport = 64
    ProviderOptions_HasNativeIAccessible = 128
    ProviderOptions_UseClientCoordinates = 256


class PropertyConditionFlags(IntFlag):
    PropertyConditionFlags_None = 0
    PropertyConditionFlags_IgnoreCase = 1
    PropertyConditionFlags_MatchSubstring = 2


class ConnectionRecoveryBehaviorOptions(IntFlag):
    ConnectionRecoveryBehaviorOptions_Disabled = 0
    ConnectionRecoveryBehaviorOptions_Enabled = 1


class CoalesceEventsOptions(IntFlag):
    CoalesceEventsOptions_Disabled = 0
    CoalesceEventsOptions_Enabled = 1


class AutomationElementMode(IntFlag):
    AutomationElementMode_None = 0
    AutomationElementMode_Full = 1


class NotificationKind(IntFlag):
    NotificationKind_ItemAdded = 0
    NotificationKind_ItemRemoved = 1
    NotificationKind_ActionCompleted = 2
    NotificationKind_ActionAborted = 3
    NotificationKind_Other = 4


class NotificationProcessing(IntFlag):
    NotificationProcessing_ImportantAll = 0
    NotificationProcessing_ImportantMostRecent = 1
    NotificationProcessing_All = 2
    NotificationProcessing_MostRecent = 3
    NotificationProcessing_CurrentThenMostRecent = 4


__all__ = [
    'UIA_BulletStyleAttributeId', 'UIA_FontSizeAttributeId',
    'ZoomUnit_LargeIncrement', 'UIA_RangeValueMaximumPropertyId',
    'TextUnit_Word', 'IUIAutomationTextEditPattern',
    'UIA_IsSpreadsheetItemPatternAvailablePropertyId',
    'SynchronizedInputType_KeyUp', 'CoalesceEventsOptions',
    'UIA_IndentationTrailingAttributeId', 'UIA_SizePropertyId',
    'UIA_IsTextPattern2AvailablePropertyId',
    'UIA_AnnotationTypesAttributeId',
    'UIA_IsSelectionPatternAvailablePropertyId',
    'UIA_SeparatorControlTypeId', 'IUIAutomationItemContainerPattern',
    'UIA_StylesFillColorPropertyId',
    'UIA_LegacyIAccessibleRolePropertyId', 'UIA_AriaRolePropertyId',
    'AnnotationType_Endnote', 'UIA_AutomationFocusChangedEventId',
    'UIA_StylesStyleNamePropertyId',
    'UIA_IsDockPatternAvailablePropertyId',
    'UIA_IsLegacyIAccessiblePatternAvailablePropertyId', 'Library',
    'UIA_LegacyIAccessibleNamePropertyId',
    'AnnotationType_AdvancedProofingIssue',
    'CoalesceEventsOptions_Enabled', 'RowOrColumnMajor_RowMajor',
    'ToggleState_Off', 'IUIAutomation', 'UIA_DocumentControlTypeId',
    'UIA_SayAsInterpretAsMetadataId',
    'StructureChangeType_ChildrenBulkRemoved',
    'UIA_IsSelectionPattern2AvailablePropertyId',
    'AnnotationType_Highlighted',
    'UIA_LegacyIAccessibleKeyboardShortcutPropertyId',
    'TextUnit_Character', 'UIA_DropTargetDropTargetEffectsPropertyId',
    'IUIAutomationSpreadsheetItemPattern', 'UIA_ValueValuePropertyId',
    'NavigateDirection_Parent', 'UIA_Transform2ZoomMinimumPropertyId',
    'UIA_InvokePatternId', 'UIA_InputReachedOtherElementEventId',
    'UIA_AfterParagraphSpacingAttributeId', 'UIA_FillColorPropertyId',
    'UIA_IndentationFirstLineAttributeId',
    'UIA_StylesStyleIdPropertyId', 'AnnotationType_GrammarError',
    'IUIAutomation3', 'UIA_Transform2ZoomMaximumPropertyId',
    'UIA_OutlineStylesAttributeId',
    'UIA_GridItemContainingGridPropertyId',
    'UIA_IsMultipleViewPatternAvailablePropertyId',
    'ProviderOptions_ClientSideProvider', 'NavigateDirection',
    'UIA_ItemContainerPatternId',
    'IUIAutomationStructureChangedEventHandler',
    'RowOrColumnMajor_ColumnMajor',
    'UIA_SpreadsheetItemFormulaPropertyId', 'UIA_IsHiddenAttributeId',
    'StructureChangeType_ChildAdded', 'UIA_TablePatternId',
    'IUIAutomationOrCondition', 'SupportedTextSelection_Single',
    'TextPatternRangeEndpoint', 'NavigateDirection_FirstChild',
    'UIA_RuntimeIdPropertyId', 'ToggleState_On',
    'UIA_RangeValueIsReadOnlyPropertyId', 'UIA_MenuOpenedEventId',
    'UIA_DataItemControlTypeId', 'UIA_SystemAlertEventId', 'ZoomUnit',
    'IUIAutomationTransformPattern2',
    'UIA_GridItemColumnSpanPropertyId',
    'UIA_MultipleViewSupportedViewsPropertyId',
    'AnnotationType_InsertionChange',
    'ProviderOptions_UseComThreading', 'StyleId_Heading8',
    'UIA_FlowsToPropertyId', 'UIA_Transform2CanZoomPropertyId',
    'UIA_StructureChangedEventId', 'IUIAutomationTextRange2',
    'UIA_IsRangeValuePatternAvailablePropertyId',
    'UIA_ListControlTypeId',
    'WindowInteractionState_BlockedByModalWindow',
    'UIA_ScrollHorizontallyScrollablePropertyId',
    'IUIAutomationGridPattern',
    'UIA_AutomationPropertyChangedEventId', 'UIA_MainLandmarkTypeId',
    'TextEditChangeType_AutoComplete', 'AnnotationType_Header',
    'UIA_Transform2ZoomLevelPropertyId',
    'UIA_LocalizedControlTypePropertyId',
    'UIA_IsTextChildPatternAvailablePropertyId',
    'UIA_SizeOfSetPropertyId', 'StructureChangeType_ChildRemoved',
    'UIA_RangeValueValuePropertyId', 'UIA_GridItemColumnPropertyId',
    'UIA_ControllerForPropertyId', 'UIA_LinkAttributeId',
    'NotificationProcessing_MostRecent', 'TreeScope_Descendants',
    'UIA_DropTargetDropTargetEffectPropertyId',
    'AnnotationType_EditingLockedChange', 'IUIAutomationTextPattern',
    'IUIAutomationEventHandler', 'UIA_Window_WindowClosedEventId',
    'UIA_SummaryChangeId', 'HeadingLevel6',
    'UIA_TableItemRowHeaderItemsPropertyId',
    'UIA_InputReachedTargetEventId',
    'StructureChangeType_ChildrenInvalidated', 'TreeScope',
    'OrientationType', 'UIA_SelectionItem_ElementSelectedEventId',
    'WindowVisualState', 'Assertive', 'UIA_CaretBidiModeAttributeId',
    'UIA_IsItalicAttributeId', 'StyleId_Heading1',
    'UIA_AppBarControlTypeId',
    'UIA_IsTransformPattern2AvailablePropertyId', 'Polite',
    'UIA_CustomLandmarkTypeId', 'CUIAutomation',
    'NotificationProcessing_ImportantAll',
    'UIA_SelectionItemPatternId', 'UIA_ControlTypePropertyId',
    'UIA_AnnotationTargetPropertyId', 'IUIAutomationStylesPattern',
    'UIA_WindowWindowInteractionStatePropertyId',
    'UIA_UnderlineStyleAttributeId',
    'UIA_ActiveTextPositionChangedEventId',
    'IUIAutomationDropTargetPattern', 'UIA_WindowControlTypeId',
    'UIA_SelectionPatternId',
    'UIA_LegacyIAccessibleChildIdPropertyId',
    'UIA_StylesExtendedPropertiesPropertyId',
    'NotificationKind_Other', 'UIA_LocalizedLandmarkTypePropertyId',
    'IUIAutomationChangesEventHandler',
    'WindowInteractionState_NotResponding', 'OrientationType_None',
    'UIA_IsExpandCollapsePatternAvailablePropertyId',
    'UIA_SynchronizedInputPatternId', 'IUIAutomationElement6',
    'DockPosition_None', 'ToggleState_Indeterminate',
    'UIA_DropTargetPatternId', 'UIA_MenuModeStartEventId', 'TextUnit',
    'UIA_FrameworkIdPropertyId', 'UIA_TreeItemControlTypeId',
    'TreeScope_Element',
    'UIA_TextEdit_ConversionTargetChangedEventId',
    'UIA_CultureAttributeId', 'UIA_SayAsInterpretAsAttributeId',
    'NavigateDirection_NextSibling', 'UIA_LayoutInvalidatedEventId',
    'IUIAutomationTableItemPattern', 'UIA_ItemTypePropertyId',
    'IUIAutomation5', 'UIA_ForegroundColorAttributeId',
    'UIA_ImageControlTypeId', 'UIA_TableItemPatternId',
    'AnnotationType_DeletionChange', 'UIA_TableControlTypeId',
    'UiaChangeInfo', 'UIA_Text_TextChangedEventId',
    'UIA_NamePropertyId', 'UIA_CaretPositionAttributeId',
    'IUIAutomationElement2', 'IUIAutomationInvokePattern',
    'UIA_CheckBoxControlTypeId', 'UIA_PaneControlTypeId',
    'AnnotationType_Footnote', 'UIA_BackgroundColorAttributeId',
    'PropertyConditionFlags',
    'UIA_LegacyIAccessibleSelectionPropertyId',
    'ZoomUnit_SmallIncrement', 'IUIAutomationElement9',
    'IUIAutomationTogglePattern', 'HeadingLevel4',
    'UIA_LegacyIAccessibleStatePropertyId', 'UIA_FontNameAttributeId',
    'IUIAutomationTransformPattern', 'UIA_SpinnerControlTypeId',
    'StructureChangeType', 'DockPosition_Right',
    'UIA_SpreadsheetItemAnnotationTypesPropertyId',
    'TextPatternRangeEndpoint_End',
    'NotificationProcessing_CurrentThenMostRecent',
    'ExpandCollapseState', 'UIA_TransformCanRotatePropertyId',
    'IUIAutomationSynchronizedInputPattern',
    'UIA_ToolTipOpenedEventId', 'UIA_AccessKeyPropertyId',
    'UIA_AnnotationTypesPropertyId',
    'UIA_ScrollVerticalScrollPercentPropertyId', 'StyleId_Heading9',
    'UIA_IsTablePatternAvailablePropertyId', 'StyleId_Subtitle',
    'TreeTraversalOptions_PostOrder', 'UIA_ValuePatternId',
    'IUIAutomationScrollItemPattern',
    'NavigateDirection_PreviousSibling', 'IUIAutomationElement5',
    'StructureChangeType_ChildrenBulkAdded',
    'UIA_LegacyIAccessibleDefaultActionPropertyId',
    'IUIAutomationAnnotationPattern', 'AnnotationType_Author',
    'UIA_Drag_DragCancelEventId', 'UIA_GridItemRowPropertyId',
    'UIA_SearchLandmarkTypeId', 'UIA_IsPasswordPropertyId',
    'UIA_StyleNameAttributeId',
    'SynchronizedInputType_RightMouseDown',
    'UIA_IsSynchronizedInputPatternAvailablePropertyId',
    'IUIAutomationTablePattern', 'UIA_AnnotationObjectsAttributeId',
    'ScrollAmount', 'StyleId_Heading4',
    'ProviderOptions_OverrideProvider',
    'IUIAutomationProxyFactoryEntry', 'TextUnit_Page',
    'UIA_LabeledByPropertyId', 'UIA_FullDescriptionPropertyId',
    'NotificationKind_ItemAdded', 'IUIAutomationDragPattern',
    'LiveSetting', 'WindowInteractionState_Closing',
    'IUIAutomationFocusChangedEventHandler', 'UIA_StyleIdAttributeId',
    'DockPosition_Bottom', 'UIA_IsDragPatternAvailablePropertyId',
    'AnnotationType_Unknown', 'UIA_TextEdit_TextChangedEventId',
    'UIA_LandmarkTypePropertyId', 'AnnotationType_TrackChanges',
    'ScrollAmount_SmallDecrement', 'UIA_GridPatternId',
    'UIA_SelectionItemIsSelectedPropertyId',
    'IUIAutomationLegacyIAccessiblePattern',
    'UIA_WindowCanMinimizePropertyId',
    'TextEditChangeType_AutoCorrect', 'SupportedTextSelection',
    'UIA_SelectionItem_ElementAddedToSelectionEventId',
    'UIA_Drag_DragStartEventId', 'TreeScope_Parent',
    'UIA_IsCustomNavigationPatternAvailablePropertyId',
    'UIA_ScrollItemPatternId',
    'UIA_IsGridItemPatternAvailablePropertyId',
    'UIA_DockDockPositionPropertyId',
    'TextEditChangeType_Composition',
    'ProviderOptions_HasNativeIAccessible',
    'UIA_IsContentElementPropertyId',
    'UIA_RangeValueLargeChangePropertyId',
    'UIA_IsWindowPatternAvailablePropertyId', 'TextUnit_Document',
    'UIA_SelectionItem_ElementRemovedFromSelectionEventId',
    'TextUnit_Format', 'UIA_LiveRegionChangedEventId',
    'DockPosition_Left', 'UIA_TableItemColumnHeaderItemsPropertyId',
    'IAccessible', 'UIA_LiveSettingPropertyId',
    'UIA_GridRowCountPropertyId',
    'ProviderOptions_UseClientCoordinates',
    'UIA_IndentationLeadingAttributeId', 'HeadingLevel_None',
    'UIA_IsScrollItemPatternAvailablePropertyId',
    'IUIAutomationMultipleViewPattern', 'UIA_CapStyleAttributeId',
    'IUIAutomationExpandCollapsePattern',
    'NotificationProcessing_ImportantMostRecent',
    'UIA_MenuClosedEventId', 'IUIAutomationTextRange3',
    'UIA_HyperlinkControlTypeId', 'HeadingLevel1',
    'UIA_IsAnnotationPatternAvailablePropertyId',
    'RowOrColumnMajor_Indeterminate', 'UIA_CalendarControlTypeId',
    'UIA_IsSubscriptAttributeId',
    'UIA_SpreadsheetItemAnnotationObjectsPropertyId',
    'IUIAutomationTextPattern2',
    'UIA_HorizontalTextAlignmentAttributeId', 'DockPosition_Fill',
    'IUIAutomationProxyFactoryMapping',
    'UIA_IsTextEditPatternAvailablePropertyId',
    'UIA_OutlineColorPropertyId', 'StyleId_Emphasis',
    'IUIAutomationElement7', 'UIA_StrikethroughColorAttributeId',
    'UIA_SelectionCanSelectMultiplePropertyId',
    'UIA_ComboBoxControlTypeId', 'IUIAutomationValuePattern',
    'IUIAutomationWindowPattern', 'AnnotationType_Footer',
    'UIA_NativeWindowHandlePropertyId',
    'UIA_DropTarget_DragLeaveEventId', 'UIA_ProcessIdPropertyId',
    'ProviderOptions_ServerSideProvider', 'UIA_TransformPattern2Id',
    'UIA_ExpandCollapsePatternId', 'IUIAutomation2',
    'UIA_TextEditPatternId', 'UIA_DragDropEffectsPropertyId',
    'UIA_ValueIsReadOnlyPropertyId',
    'UIA_IsTogglePatternAvailablePropertyId',
    'ConnectionRecoveryBehaviorOptions_Enabled', 'RowOrColumnMajor',
    'UIA_IsDropTargetPatternAvailablePropertyId',
    'AnnotationType_SpellingError', 'UIA_IsPeripheralPropertyId',
    'AnnotationType_UnsyncedChange', 'CUIAutomation8',
    'UIA_IsObjectModelPatternAvailablePropertyId',
    'UIA_LegacyIAccessiblePatternId',
    'UIA_AnnotationDateTimePropertyId',
    'UIA_SemanticZoomControlTypeId',
    'UIA_Selection2CurrentSelectedItemPropertyId',
    'IUIAutomationCondition', 'IUIAutomationSelectionPattern2',
    'UIA_ScrollPatternId', 'UIA_RangeValuePatternId',
    'UIA_MenuModeEndEventId', 'HeadingLevel9',
    'UIA_WindowIsModalPropertyId', 'UIA_MenuItemControlTypeId',
    'UIA_MarginBottomAttributeId',
    'UIA_WindowWindowVisualStatePropertyId', 'UIA_TreeControlTypeId',
    'UIA_HostedFragmentRootsInvalidatedEventId', 'StyleId_Heading6',
    'IUIAutomationTreeWalker', 'typelib_path',
    'UIA_ItemStatusPropertyId', 'IUIAutomationPropertyCondition',
    'NavigateDirection_LastChild', 'SynchronizedInputType',
    'IUIAutomationElementArray', 'UIA_IsDialogPropertyId',
    'UIA_FontWeightAttributeId',
    'UIA_ScrollHorizontalViewSizePropertyId',
    'UIA_ProviderDescriptionPropertyId', 'IUIAutomationElement',
    'NotificationKind_ActionAborted',
    'UIA_TableRowOrColumnMajorPropertyId',
    'IUIAutomationCustomNavigationPattern', 'ScrollAmount_NoAmount',
    'PropertyConditionFlags_MatchSubstring',
    'ConnectionRecoveryBehaviorOptions_Disabled', 'IUIAutomation6',
    'UIA_ThumbControlTypeId', 'UIA_ButtonControlTypeId',
    'UIA_Drag_DragCompleteEventId', 'TextEditChangeType',
    'IUIAutomationElement4', 'AnnotationType_Comment',
    'ExpandCollapseState_PartiallyExpanded',
    'UIA_IsSelectionItemPatternAvailablePropertyId',
    'UIA_LegacyIAccessibleValuePropertyId', 'ZoomUnit_SmallDecrement',
    'SupportedTextSelection_None',
    'UIA_IsVirtualizedItemPatternAvailablePropertyId',
    'TreeScope_Ancestors', 'UIA_Selection_InvalidatedEventId',
    'UIA_IsDataValidForFormPropertyId', 'HeadingLevel2',
    'UIA_TableColumnHeadersPropertyId', 'ScrollAmount_LargeDecrement',
    'UIA_Window_WindowOpenedEventId', 'UIA_EditControlTypeId',
    'UIA_Invoke_InvokedEventId', 'AutomationElementMode',
    'TreeTraversalOptions_LastToFirstOrder', 'IUIAutomationElement3',
    'CoalesceEventsOptions_Disabled',
    'UIA_TextFlowDirectionsAttributeId',
    'UIA_OverlineColorAttributeId',
    'UIA_ExpandCollapseExpandCollapseStatePropertyId',
    'UIA_IsEnabledPropertyId', 'ExpandCollapseState_Expanded',
    'UIA_IsSuperscriptAttributeId',
    'AnnotationType_DataValidationError', 'UIA_FormLandmarkTypeId',
    'IUIAutomationSelectionPattern', 'AutomationElementMode_None',
    'AnnotationType_MoveChange', 'UIA_CustomNavigationPatternId',
    'AnnotationType_ConflictingChange', 'UIA_MultipleViewPatternId',
    'SynchronizedInputType_KeyDown', 'UIA_StatusBarControlTypeId',
    'UIA_OrientationPropertyId', 'UIA_TextPatternId',
    'NotificationKind', 'UIA_WindowCanMaximizePropertyId',
    'AnnotationType_FormatChange', 'UIA_ToolBarControlTypeId',
    'TextEditChangeType_None', 'UIA_OverlineStyleAttributeId',
    'IUIAutomationSelectionItemPattern', 'IUIAutomationElement8',
    'IUIAutomationNotificationEventHandler',
    'UIA_AnnotationAnnotationTypeIdPropertyId',
    'IUIAutomationVirtualizedItemPattern',
    'IUIAutomationScrollPattern', 'UIA_ToolTipControlTypeId',
    'UIA_FillTypePropertyId', 'UIA_DragDropEffectPropertyId',
    'UIA_ScrollHorizontalScrollPercentPropertyId',
    'UIA_TextChildPatternId', 'UIA_DragPatternId',
    'SynchronizedInputType_LeftMouseDown',
    'UIA_TableRowHeadersPropertyId',
    'UIA_IsKeyboardFocusablePropertyId',
    'ExpandCollapseState_Collapsed',
    'UIA_BoundingRectanglePropertyId', 'UIA_WindowPatternId',
    'UIA_CustomControlTypeId',
    'ProviderOptions_NonClientAreaProvider',
    'UIA_IsControlElementPropertyId',
    'IUIAutomationPropertyChangedEventHandler',
    'UIA_RangeValueSmallChangePropertyId',
    'NotificationKind_ActionCompleted',
    'UIA_StrikethroughStyleAttributeId', 'UIA_TextPattern2Id',
    'UIA_TransformPatternId', 'ExtendedProperty',
    'UIA_OutlineThicknessPropertyId',
    'TextPatternRangeEndpoint_Start',
    'UIA_IsTableItemPatternAvailablePropertyId',
    'UIA_IsReadOnlyAttributeId', 'UIA_SelectionPattern2Id',
    'ProviderOptions', 'UIA_InputDiscardedEventId',
    'UIA_StylesPatternId', 'UIA_ChangesEventId',
    'UIA_IsOffscreenPropertyId',
    'WindowInteractionState_ReadyForUserInteraction',
    'OrientationType_Horizontal', 'HeadingLevel8',
    'UIA_IsTransformPatternAvailablePropertyId', 'Off',
    'AutomationElementMode_Full', 'AnnotationType_FormulaError',
    'ExpandCollapseState_LeafNode', 'UIA_TabItemControlTypeId',
    'UIA_NotificationEventId', 'StyleId_Normal',
    'UIA_DropTarget_DragEnterEventId',
    'UIA_StylesFillPatternColorPropertyId',
    'UIA_AnnotationAuthorPropertyId', 'UIA_MarginTrailingAttributeId',
    'UIA_VisualEffectsPropertyId', 'UIA_ClassNamePropertyId',
    'IUIAutomationEventHandlerGroup',
    'UIA_LegacyIAccessibleDescriptionPropertyId',
    'UIA_MenuBarControlTypeId',
    'UIA_Selection2FirstSelectedItemPropertyId', 'StyleId_Heading5',
    'UIA_IsGridPatternAvailablePropertyId',
    'IUIAutomationTextEditTextChangedEventHandler', 'TextUnit_Line',
    'OrientationType_Vertical', 'UIA_UnderlineColorAttributeId',
    'UIA_HeaderItemControlTypeId', 'StyleId_BulletedList',
    'UIA_SelectionIsSelectionRequiredPropertyId',
    'UIA_GridItemRowSpanPropertyId', 'UIA_TextControlTypeId',
    'ToggleState', 'IUIAutomationObjectModelPattern',
    'WindowInteractionState_Running', 'IUIAutomationAndCondition',
    'UIA_LevelPropertyId', 'UIA_IsScrollPatternAvailablePropertyId',
    'IUIAutomation4', 'StructureChangeType_ChildrenReordered',
    'UIA_TransformCanMovePropertyId', 'UIA_DragIsGrabbedPropertyId',
    'StyleId_Custom', 'AnnotationType_ExternalChange',
    'ConnectionRecoveryBehaviorOptions',
    'PropertyConditionFlags_IgnoreCase', 'UIA_SliderControlTypeId',
    'UIA_DropTarget_DroppedEventId', 'UIA_GridItemPatternId',
    'IUIAutomationActiveTextPositionChangedEventHandler',
    'UIA_IsStylesPatternAvailablePropertyId',
    'UIA_HeadingLevelPropertyId', 'UIA_FlowsFromPropertyId',
    'NotificationProcessing_All', 'ZoomUnit_LargeDecrement',
    'UIA_MarginLeadingAttributeId',
    'UIA_IsSpreadsheetPatternAvailablePropertyId',
    'UIA_RadioButtonControlTypeId', 'UIA_SplitButtonControlTypeId',
    'UIA_DockPatternId', 'StyleId_Quote',
    'ProviderOptions_RefuseNonClientSupport',
    'UIA_IsActiveAttributeId', 'TreeScope_Subtree',
    'UIA_MenuControlTypeId', 'UIA_RangeValueMinimumPropertyId',
    'UIA_PositionInSetPropertyId',
    'UIA_IsItemContainerPatternAvailablePropertyId',
    'UIA_SpreadsheetItemPatternId', 'IUIAutomationCacheRequest',
    'IUIAutomationTextRangeArray', 'NotificationKind_ItemRemoved',
    'UIA_OptimizeForVisualContentPropertyId',
    'IUIAutomationTextChildPattern', 'HeadingLevel3',
    'UIA_ProgressBarControlTypeId', 'UIA_MarginTopAttributeId',
    'UIA_LegacyIAccessibleHelpPropertyId',
    'PropertyConditionFlags_None', 'TreeScope_None',
    'UIA_LineSpacingAttributeId', 'IUIAutomationNotCondition',
    'ScrollAmount_SmallIncrement',
    'UIA_Selection2ItemCountPropertyId', 'UIA_DescribedByPropertyId',
    'UIA_TitleBarControlTypeId', 'StyleId_Title',
    'UIA_SelectionItemSelectionContainerPropertyId',
    'WindowInteractionState', 'UIA_CenterPointPropertyId',
    'IUIAutomationSpreadsheetPattern', 'UIA_CulturePropertyId',
    'UIA_IsRequiredForFormPropertyId', 'ZoomUnit_NoAmount',
    'UIA_SpreadsheetPatternId', 'HeadingLevel5',
    'UIA_AnimationStyleAttributeId', 'UIA_WindowIsTopmostPropertyId',
    'ProviderOptions_ProviderOwnsSetFocus', 'TreeScope_Children',
    'DockPosition', 'UIA_ListItemControlTypeId',
    'UIA_StylesFillPatternStylePropertyId',
    'IRawElementProviderSimple',
    'UIA_ScrollVerticallyScrollablePropertyId',
    'WindowVisualState_Normal', 'UIA_DataGridControlTypeId',
    'UIA_SelectionSelectionPropertyId',
    'IUIAutomationRangeValuePattern', 'UIA_HeaderControlTypeId',
    'WindowVisualState_Maximized', 'UIA_NavigationLandmarkTypeId',
    'UIA_ScrollBarControlTypeId',
    'UIA_IsInvokePatternAvailablePropertyId',
    'IUIAutomationGridItemPattern', 'SupportedTextSelection_Multiple',
    'UIA_HasKeyboardFocusPropertyId', 'WindowVisualState_Minimized',
    'UIA_Text_TextSelectionChangedEventId', 'UIA_GroupControlTypeId',
    'HeadingLevel7', 'UIA_AutomationIdPropertyId',
    'UIA_AsyncContentLoadedEventId', 'IUIAutomationTextRange',
    'UIA_TransformCanResizePropertyId',
    'SynchronizedInputType_RightMouseUp', 'AnnotationType_Sensitive',
    'UIA_MultipleViewCurrentViewPropertyId',
    'AnnotationType_Mathematics', 'UIA_ClickablePointPropertyId',
    'TextEditChangeType_CompositionFinalized', 'UIA_TabControlTypeId',
    'AnnotationType_CircularReferenceError',
    'IUIAutomationBoolCondition', 'UIA_AnnotationObjectsPropertyId',
    'DockPosition_Top', 'UIA_AcceleratorKeyPropertyId',
    'StyleId_NumberedList', 'UIA_ToolTipClosedEventId',
    'StyleId_Heading3', 'UIA_TogglePatternId',
    'IUIAutomationDockPattern',
    'UIA_AnnotationAnnotationTypeNamePropertyId',
    'UIA_ScrollVerticalViewSizePropertyId', 'StyleId_Heading2',
    'UIA_AriaPropertiesPropertyId', 'UIA_RotationPropertyId',
    'UIA_HelpTextPropertyId', 'UIA_BeforeParagraphSpacingAttributeId',
    'UIA_ObjectModelPatternId', 'UIA_GridColumnCountPropertyId',
    'ScrollAmount_LargeIncrement', 'NotificationProcessing',
    'TreeTraversalOptions_Default',
    'UIA_SelectionActiveEndAttributeId', 'TreeTraversalOptions',
    'TextUnit_Paragraph', 'IUIAutomationProxyFactory',
    'UIA_IsTextPatternAvailablePropertyId',
    'UIA_StylesShapePropertyId', 'UIA_VirtualizedItemPatternId',
    'UIA_AnnotationPatternId', 'UIA_DragGrabbedItemsPropertyId',
    'SynchronizedInputType_LeftMouseUp', 'UIA_TabsAttributeId',
    'UIA_ToggleToggleStatePropertyId', 'StyleId_Heading7',
    'UIA_Selection2LastSelectedItemPropertyId',
    'UIA_IsValuePatternAvailablePropertyId'
]

