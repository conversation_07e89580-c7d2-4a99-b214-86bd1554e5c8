from enum import IntFlag

import comtypes.gen._00020430_0000_0000_C000_000000000046_0_2_0 as __wrapper_module__
from comtypes.gen._00020430_0000_0000_C000_000000000046_0_2_0 import (
    Gray, VgaColor, IPicture, Picture, CoClass, typelib_path,
    OLE_HANDLE, <PERSON>ONTNAME, FONTITALIC, Font, OLE_YSIZE_PIXELS,
    OLE_XPOS_HIMETRIC, dispid, OLE_YPOS_CONTAINER, _lcid, Library,
    IPictureDisp, OLE_XPOS_PIXELS, OLE_OPTEXCLUSIVE, OLE_XSIZE_PIXELS,
    StdFont, Monochrome, <PERSON>LE_COLOR, OLE_YPOS_HIMETRIC,
    OLE_XSIZE_HIMETRIC, Default, Unchecked, FON<PERSON><PERSON>ERSCORE, FONTBOLD,
    Color, VARIANT_BOOL, OLE_ENABLEDEFAULTBOOL, IFontDisp,
    <PERSON>LE_CANCELBOOL, DISPPROPERTY, BSTR, EXCEPINFO, _check_version,
    FontEvents, IFont, CO<PERSON>ETHOD, <PERSON><PERSON><PERSON>LT, OLE_YPOS_PIXELS, IUnknown,
    StdPicture, OLE_YSIZE_CONTAINER, IDispatch, GUID, DISPMETHOD,
    IEnumVARIANT, Checked, FONTSTRIKETHROUGH, OLE_XPOS_CONTAINER,
    IFontEventsDisp, FONTSIZE, OLE_YSIZE_HIMETRIC,
    OLE_XSIZE_CONTAINER, DISPPARAMS
)


class OLE_TRISTATE(IntFlag):
    Unchecked = 0
    Checked = 1
    Gray = 2


class LoadPictureConstants(IntFlag):
    Default = 0
    Monochrome = 1
    VgaColor = 2
    Color = 4


__all__ = [
    'OLE_XSIZE_HIMETRIC', 'Gray', 'Default', 'Unchecked', 'VgaColor',
    'IPicture', 'FONTBOLD', 'FONTUNDERSCORE', 'Picture', 'Color',
    'OLE_ENABLEDEFAULTBOOL', 'LoadPictureConstants', 'typelib_path',
    'IFontDisp', 'OLE_CANCELBOOL', 'OLE_HANDLE', 'FONTNAME',
    'FONTITALIC', 'Font', 'OLE_YSIZE_PIXELS', 'OLE_TRISTATE',
    'OLE_XPOS_HIMETRIC', 'FontEvents', 'IFont', 'OLE_YPOS_CONTAINER',
    'OLE_YPOS_PIXELS', 'StdPicture', 'Library', 'IPictureDisp',
    'OLE_YSIZE_CONTAINER', 'OLE_XPOS_PIXELS', 'OLE_OPTEXCLUSIVE',
    'OLE_XSIZE_PIXELS', 'Checked', 'FONTSTRIKETHROUGH', 'StdFont',
    'OLE_XPOS_CONTAINER', 'Monochrome', 'OLE_COLOR',
    'OLE_YPOS_HIMETRIC', 'IFontEventsDisp', 'FONTSIZE',
    'OLE_YSIZE_HIMETRIC', 'OLE_XSIZE_CONTAINER'
]

