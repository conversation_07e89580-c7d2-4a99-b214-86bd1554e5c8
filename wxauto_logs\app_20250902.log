2025-09-02 14:41:00 [wxauto] [DEBUG] [wx.py:31]  开始监听
2025-09-02 14:41:00 [wxauto] [DEBUG] [sessionbox.py:68]  切换聊天窗口: 文件传输助手, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, 0.5
2025-09-02 14:41:01 [wxauto] [DEBUG] [sessionbox.py:128]  文件传输助手 完整匹配
2025-09-02 14:41:02 [wxauto] [DEBUG] [main.py:192]  文件传输助手 切换到聊天窗口: 文件传输助手
2025-09-02 14:41:02 [wxauto] [DEBUG] [sessionbox.py:152]  打开独立窗口: 文件传输助手
2025-09-02 14:41:02 [wxauto] [DEBUG] [sessionbox.py:156]  找到会话: 文件传输助手
2025-09-02 14:42:30 [wxauto] [DEBUG] [wx.py:31]  开始监听
2025-09-02 14:42:30 [wxauto] [DEBUG] [sessionbox.py:68]  切换聊天窗口: 文件传输助手, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, 0.5
2025-09-02 14:42:31 [wxauto] [DEBUG] [msg.py:79]  content: 你好, length: 8
2025-09-02 14:42:32 [wxauto] [DEBUG] [msg.py:79]  content: 全家好啊, length: 9
2025-09-02 14:43:20 [wxauto] [DEBUG] [wx.py:31]  开始监听
2025-09-02 14:43:20 [wxauto] [DEBUG] [sessionbox.py:68]  切换聊天窗口: 文件传输助手, False, False, 0.5
2025-09-02 14:43:21 [wxauto] [DEBUG] [main.py:192]  文件传输助手 切换到聊天窗口: 文件传输助手
2025-09-02 14:43:21 [wxauto] [DEBUG] [sessionbox.py:152]  打开独立窗口: 文件传输助手
2025-09-02 14:43:22 [wxauto] [DEBUG] [sessionbox.py:156]  找到会话: 文件传输助手
2025-09-02 14:43:36 [wxauto] [DEBUG] [wx.py:31]  开始监听
2025-09-02 14:43:36 [wxauto] [DEBUG] [sessionbox.py:68]  切换聊天窗口: 文件传输助手, False, False, 0.5
2025-09-02 14:43:37 [wxauto] [DEBUG] [main.py:192]  文件传输助手 切换到聊天窗口: 文件传输助手
2025-09-02 14:43:37 [wxauto] [DEBUG] [sessionbox.py:152]  打开独立窗口: 文件传输助手
2025-09-02 14:43:37 [wxauto] [DEBUG] [sessionbox.py:156]  找到会话: 文件传输助手
